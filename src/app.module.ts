import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PostsModule } from './posts/posts.module';
import envConfig from '../config/env';
import { PostsEntity } from './posts/entities/post.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [envConfig.path],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configServer: ConfigService) => ({
        // 数据库类型
        type: 'mysql',
        // 数据表实体，synchronize为true时，自动创建表，生产环境建议关闭
        entities: [PostsEntity],
        // 主机，默认为localhost
        host: configServer.get('DB_HOST'),
        // 端口号
        port: configServer.get('DB_PORT'),
        // 用户名
        username: configServer.get('DB_USER'),
        // 密码
        password: configServer.get('DB_PASSWD'),
        // 数据库名
        database: configServer.get('DB_DATABASE'),
        // 服务器上配置的时区
        timezone: '+08:00',
        // 根据实体自动创建数据库表， 生产环境建议关闭
        synchronize: true,
      }),
    }),
    PostsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
