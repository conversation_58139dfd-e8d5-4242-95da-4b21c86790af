import { Injectable, HttpException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { PostsEntity } from './entities/post.entity';

export interface PostsRo {
  list: PostsEntity[];
  count: number;
  totalPages: number;
  currentPage: number;
}

@Injectable()
export class PostsService {
  constructor(
    @InjectRepository(PostsEntity)
    private readonly postsRepository: Repository<PostsEntity>,
  ) {}

  // 创建文章
  async create(posts: Partial<PostsEntity>): Promise<PostsEntity> {
    const { title } = posts;
    if (!title) {
      throw new HttpException('缺少文章标题', 401);
    }
    const doc = await this.postsRepository.findOne({ where: { title } });
    if (doc) {
      throw new HttpException('文章已存在', 401);
    }
    return await this.postsRepository.save(posts);
  }

  // 获取文章列表
  async findAll(page: number = 1, pageSize: number = 10): Promise<PostsRo> {
    const [posts, totalCount] = await this.postsRepository.findAndCount({
      // 分页偏移量
      skip: (page - 1) * pageSize,
      // 每页显示的记录数
      take: pageSize,
      order: { create_time: 'DESC' },
    });

    return {
      list: posts,
      count: totalCount,
      // 计算总页数
      totalPages: Math.ceil(totalCount / pageSize),
      currentPage: page,
    };
  }

  findOne(id: number) {
    return `This action returns a #${id} post`;
  }

  update(id: number, updatePostDto: UpdatePostDto) {
    return `This action updates a #${id} post`;
  }

  remove(id: number) {
    return `This action removes a #${id} post`;
  }
}
